using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using System.ComponentModel;
using System.Collections.Generic;

namespace DriverManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel للشاشة الاحترافية لإدارة السائقين والعروض
    /// </summary>
    public class ProfessionalDriversViewModel : BindableBase, IDisposable
    {
        private readonly ObservableCollection<DriverOffer> _originalDriverOffers;
        private string _visitNumber;
        private int _daysCount;
        private string _searchText = string.Empty;
        private string _selectedVehicleTypeFilter = "الكل";
        private string _selectedStatusFilter = "الكل";
        private DriverOffer _selectedDriver;
        private bool _hasUnsavedChanges = false;

        public ProfessionalDriversViewModel(ObservableCollection<DriverOffer> driverOffers, string visitNumber, int daysCount)
        {
            _originalDriverOffers = driverOffers ?? new ObservableCollection<DriverOffer>();
            _visitNumber = visitNumber ?? string.Empty;
            _daysCount = daysCount;

            // تهيئة المجموعات
            FilteredDrivers = new ObservableCollection<DriverOffer>();
            VehicleTypes = new ObservableCollection<string>();
            StatusOptions = new ObservableCollection<string>();

            // تهيئة الأوامر
            InitializeCommands();

            // تحميل البيانات الأولية
            LoadInitialData();

            // مراقبة التغييرات
            FilteredDrivers.CollectionChanged += (s, e) => RaisePropertyChanged(nameof(HasUnsavedChanges));
        }

        #region Properties

        public ObservableCollection<DriverOffer> FilteredDrivers { get; }
        public ObservableCollection<string> VehicleTypes { get; }
        public ObservableCollection<string> StatusOptions { get; }

        public string VisitInfo => $"الزيارة رقم: {_visitNumber} | عدد الأيام: {_daysCount}";

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    ApplyFilters();
                }
            }
        }

        public string SelectedVehicleTypeFilter
        {
            get => _selectedVehicleTypeFilter;
            set
            {
                if (SetProperty(ref _selectedVehicleTypeFilter, value))
                {
                    ApplyFilters();
                }
            }
        }

        public string SelectedStatusFilter
        {
            get => _selectedStatusFilter;
            set
            {
                if (SetProperty(ref _selectedStatusFilter, value))
                {
                    ApplyFilters();
                }
            }
        }

        public DriverOffer SelectedDriver
        {
            get => _selectedDriver;
            set => SetProperty(ref _selectedDriver, value);
        }

        public bool HasUnsavedChanges
        {
            get => _hasUnsavedChanges;
            set => SetProperty(ref _hasUnsavedChanges, value);
        }

        // الإحصائيات
        public int TotalDrivers => FilteredDrivers.Count;
        public int SelectedDrivers => FilteredDrivers.Count(d => d.IsSelected);
        public decimal AveragePrice => FilteredDrivers.Any() ? FilteredDrivers.Average(d => d.ProposedAmount) : 0;
        public decimal LowestPrice => FilteredDrivers.Any() ? FilteredDrivers.Min(d => d.ProposedAmount) : 0;
        public decimal HighestPrice => FilteredDrivers.Any() ? FilteredDrivers.Max(d => d.ProposedAmount) : 0;
        public decimal TotalAmount => FilteredDrivers.Where(d => d.IsSelected).Sum(d => d.ProposedAmount);
        public int WinnersCount => FilteredDrivers.Count(d => d.IsWinner);

        #endregion

        #region Commands

        public DelegateCommand ApplyFiltersCommand { get; private set; }
        public DelegateCommand FilterByPriceCommand { get; private set; }
        public DelegateCommand ApproveWinnersCommand { get; private set; }
        public DelegateCommand AnalyzeOffersCommand { get; private set; }
        public DelegateCommand SendMessagesCommand { get; private set; }
        public DelegateCommand ClearSelectionCommand { get; private set; }
        public DelegateCommand ExportToExcelCommand { get; private set; }
        public DelegateCommand PrintReportCommand { get; private set; }
        public DelegateCommand SaveChangesCommand { get; private set; }

        #endregion

        #region Private Methods

        private void InitializeCommands()
        {
            ApplyFiltersCommand = new DelegateCommand(ApplyFilters);
            FilterByPriceCommand = new DelegateCommand(ExecuteFilterByPrice);
            ApproveWinnersCommand = new DelegateCommand(ExecuteApproveWinners);
            AnalyzeOffersCommand = new DelegateCommand(ExecuteAnalyzeOffers);
            SendMessagesCommand = new DelegateCommand(ExecuteSendMessages);
            ClearSelectionCommand = new DelegateCommand(ExecuteClearSelection);
            ExportToExcelCommand = new DelegateCommand(ExecuteExportToExcel);
            PrintReportCommand = new DelegateCommand(ExecutePrintReport);
            SaveChangesCommand = new DelegateCommand(ExecuteSaveChanges, CanExecuteSaveChanges);
        }

        private void LoadInitialData()
        {
            try
            {
                // تحميل أنواع السيارات
                VehicleTypes.Clear();
                VehicleTypes.Add("الكل");
                var vehicleTypes = _originalDriverOffers
                    .Where(d => !string.IsNullOrEmpty(d.VehicleType))
                    .Select(d => d.VehicleType)
                    .Distinct()
                    .OrderBy(v => v);
                
                foreach (var type in vehicleTypes)
                {
                    VehicleTypes.Add(type);
                }

                // تحميل خيارات الحالة
                StatusOptions.Clear();
                StatusOptions.Add("الكل");
                StatusOptions.Add("تم التقديم");
                StatusOptions.Add("🏆 فائز");
                StatusOptions.Add("✅ معتمد");
                StatusOptions.Add("❌ مرفوض");
                StatusOptions.Add("⏳ قيد المراجعة");
                StatusOptions.Add("🚫 ملغي");

                // تطبيق الفلاتر الأولية
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyFilters()
        {
            try
            {
                var filtered = _originalDriverOffers.AsEnumerable();

                // فلتر نوع السيارة
                if (!string.IsNullOrEmpty(SelectedVehicleTypeFilter) && SelectedVehicleTypeFilter != "الكل")
                {
                    filtered = filtered.Where(d => d.VehicleType == SelectedVehicleTypeFilter);
                }

                // فلتر الحالة
                if (!string.IsNullOrEmpty(SelectedStatusFilter) && SelectedStatusFilter != "الكل")
                {
                    filtered = filtered.Where(d => d.OfferStatus == SelectedStatusFilter);
                }

                // فلتر البحث
                if (!string.IsNullOrEmpty(SearchText))
                {
                    var searchLower = SearchText.ToLower();
                    filtered = filtered.Where(d => 
                        d.DriverName.ToLower().Contains(searchLower) ||
                        d.DriverCode.ToLower().Contains(searchLower) ||
                        d.PhoneNumber.Contains(SearchText) ||
                        d.VehicleType.ToLower().Contains(searchLower));
                }

                // تحديث القائمة المفلترة
                FilteredDrivers.Clear();
                foreach (var driver in filtered.OrderBy(d => d.ProposedAmount))
                {
                    FilteredDrivers.Add(driver);
                }

                // تحديث الإحصائيات
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            RaisePropertyChanged(nameof(TotalDrivers));
            RaisePropertyChanged(nameof(SelectedDrivers));
            RaisePropertyChanged(nameof(AveragePrice));
            RaisePropertyChanged(nameof(LowestPrice));
            RaisePropertyChanged(nameof(HighestPrice));
            RaisePropertyChanged(nameof(TotalAmount));
            RaisePropertyChanged(nameof(WinnersCount));
        }

        #endregion

        #region Command Implementations

        private void ExecuteFilterByPrice()
        {
            try
            {
                if (!FilteredDrivers.Any())
                {
                    MessageBox.Show("لا توجد عروض للفلترة", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // ترتيب حسب السعر وتحديد أقل الأسعار
                var sortedOffers = FilteredDrivers.OrderBy(o => o.ProposedAmount).ToList();
                
                // إلغاء تحديد الكل أولاً
                foreach (var offer in FilteredDrivers)
                {
                    offer.IsSelected = false;
                }

                // تحديد أقل 3 أسعار
                var topOffers = sortedOffers.Take(3);
                foreach (var offer in topOffers)
                {
                    offer.IsSelected = true;
                }

                HasUnsavedChanges = true;
                UpdateStatistics();

                MessageBox.Show($"تم تحديد أقل {topOffers.Count()} أسعار تلقائياً", "نجح", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فلترة الأسعار: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExecuteApproveWinners()
        {
            try
            {
                var winners = FilteredDrivers.Where(d => d.OfferStatus == "🏆 فائز").ToList();
                
                if (!winners.Any())
                {
                    MessageBox.Show("لا توجد عروض بحالة 'فائز' لاعتمادها", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                foreach (var winner in winners)
                {
                    winner.OfferStatus = "✅ معتمد";
                    winner.IsSelected = true;
                    winner.IsWinner = true;
                }

                HasUnsavedChanges = true;
                UpdateStatistics();

                MessageBox.Show($"تم اعتماد {winners.Count} سائق فائز", "نجح", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اعتماد الفائزين: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExecuteAnalyzeOffers()
        {
            try
            {
                var analysis = $@"📊 تحليل العروض المقدمة:

🔢 إجمالي العروض: {TotalDrivers}
✅ العروض المختارة: {SelectedDrivers}
🏆 السائقين الفائزين: {WinnersCount}

💰 تحليل الأسعار:
• أقل سعر: {LowestPrice:N0} ريال
• أعلى سعر: {HighestPrice:N0} ريال  
• متوسط السعر: {AveragePrice:N0} ريال
• إجمالي المبالغ المختارة: {TotalAmount:N0} ريال

📈 التوصيات:
• نسبة التوفير المحتملة: {((HighestPrice - LowestPrice) / HighestPrice * 100):F1}%
• عدد العروض المناسبة: {FilteredDrivers.Count(d => d.ProposedAmount <= AveragePrice)}";

                MessageBox.Show(analysis, "📊 تحليل العروض", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحليل العروض: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExecuteSendMessages()
        {
            try
            {
                var selectedDrivers = FilteredDrivers.Where(d => d.IsSelected).ToList();
                
                if (!selectedDrivers.Any())
                {
                    MessageBox.Show("يرجى تحديد السائقين المراد إرسال رسائل لهم", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // هنا يمكن فتح نافذة إرسال الرسائل
                MessageBox.Show($"سيتم إرسال رسائل لـ {selectedDrivers.Count} سائق", "إرسال الرسائل", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال الرسائل: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExecuteClearSelection()
        {
            try
            {
                foreach (var driver in FilteredDrivers)
                {
                    driver.IsSelected = false;
                }

                HasUnsavedChanges = true;
                UpdateStatistics();

                MessageBox.Show("تم مسح جميع التحديدات", "نجح", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في مسح التحديد: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExecuteExportToExcel()
        {
            try
            {
                // هنا يمكن إضافة كود تصدير Excel
                MessageBox.Show("سيتم تصدير البيانات إلى Excel", "تصدير", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExecutePrintReport()
        {
            try
            {
                // هنا يمكن إضافة كود الطباعة
                MessageBox.Show("سيتم طباعة التقرير", "طباعة", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExecuteSaveChanges()
        {
            try
            {
                // حفظ التغييرات
                HasUnsavedChanges = false;
                
                MessageBox.Show("تم حفظ التغييرات بنجاح", "حفظ", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool CanExecuteSaveChanges()
        {
            return HasUnsavedChanges;
        }

        #endregion

        #region Public Methods

        public void RefreshData()
        {
            ApplyFilters();
        }

        public void UpdateLayout(double width, double height)
        {
            // تحديث التخطيط حسب حجم النافذة
        }

        public void Dispose()
        {
            // تنظيف الموارد
        }

        #endregion
    }
}
